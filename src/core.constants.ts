export enum CollectionStatus {
  PREMARKET = "PREMARKET",
  MARKET = "MARKET",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: Date;
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PREMARKET]: "Pre-market",
  [CollectionStatus.MARKET]: "Market",
  [CollectionStatus.DELETED]: "Deleted",
};

export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  name?: string;
  role?: "admin" | "user";
  tg_id?: string;
  ton_wallet_address?: string;
  referral_id?: string; // Telegram ID of the user who referred this user
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderEntity {
  id?: string;
  number?: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId: string;
  productId: string;
  amount: number;
  status: OrderStatus;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TxLookup {
  last_checked_record_id: string;
}
